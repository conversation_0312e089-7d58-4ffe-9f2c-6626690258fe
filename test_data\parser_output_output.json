{"success": true, "message": "Rule extraction completed successfully", "parsing_confidence": 0.92, "processing_time_ms": 4500, "rules_json": {"rule_1": {"description": "OWNERSHIP_PERCENTAGE must not be null or empty", "field": "OWNERSHIP_PERCENTAGE", "validation_type": "completeness", "severity": "critical"}, "rule_2": {"description": "NAV must be a positive number", "field": "NAV", "validation_type": "data_type", "severity": "critical"}, "rule_3": {"description": "UNIQUE_ID must be unique across all records", "field": "UNIQUE_ID", "validation_type": "uniqueness", "severity": "critical"}, "rule_4": {"description": "PORTFOLIO_ID must follow format PORT_XXX", "field": "PORTFOLIO_ID", "validation_type": "format", "severity": "warning"}, "rule_5": {"description": "UNIQUE_ID must not be null or empty", "field": "UNIQUE_ID", "validation_type": "completeness", "severity": "critical"}, "rule_6": {"description": "PERIOD must be in format QX-YYYY", "field": "PERIOD", "validation_type": "format", "severity": "warning"}, "rule_7": {"description": "NO_OF_SHARES must be a positive integer", "field": "NO_OF_SHARES", "validation_type": "data_type", "severity": "warning"}, "rule_8": {"description": "CAPITAL_CALLED must not be null", "field": "CAPITAL_CALLED", "validation_type": "completeness", "severity": "critical"}}, "column_template": {"UNIQUE_ID": {"type": "string", "required": true, "unique": true}, "PORTFOLIO_ID": {"type": "string", "required": true, "pattern": "PORT_[0-9]{3}"}, "REGISTERED_HOLDER": {"type": "string", "required": true}, "PERIOD": {"type": "string", "required": true, "pattern": "Q[1-4]-[0-9]{4}"}, "FUND_NAME": {"type": "string", "required": true}, "NAV": {"type": "number", "required": true, "minimum": 0}, "OWNERSHIP_PERCENTAGE": {"type": "number", "required": true, "minimum": 0, "maximum": 100}, "CAPITAL_CALLED": {"type": "number", "required": true, "minimum": 0}, "NO_OF_SHARES": {"type": "number", "required": true, "minimum": 0}, "COMMITTED_CAPITAL": {"type": "number", "required": true, "minimum": 0}}, "metadata": {"total_rules_extracted": 8, "critical_rules": 5, "warning_rules": 3, "extraction_timestamp": "2025-01-14T10:30:00Z", "source_document": "CBRE_Fund_Validation_Rules.pdf"}, "logs": {"master_log": "logs/master.log", "rule_extraction_log": "logs/rule_extraction.log", "parsing_log": "logs/parsing.log"}}