ESCALATION DECISION FRAMEWORK

You are determining whether human escalation is needed based on audit findings.

AGENT: {agent_name}
PHASE: {agent_phase}
AUDIT FINDINGS: {findings}
ESCALATION THRESHOLDS: {escalation_thresholds}

ESCALATION TRIGGERS:

IMMEDIATE ESCALATION (CRITICAL):
- Security violations or data leakage detected
- Schema violations in critical fields
- High-confidence hallucinations detected
- Agent exceeded processing time limits significantly
- Validation coverage below minimum thresholds

CONDITIONAL ESCALATION (WARNING):
- Multiple minor schema violations
- Low-confidence hallucinations
- Performance degradation patterns
- Unusual override rates in DataValidationAgent
- Parsing confidence below thresholds in SP_Parser_Agent

NO ESCALATION (PASSED):
- All checks passed within acceptable ranges
- Minor formatting issues only
- Performance within normal parameters

AGENT-SPECIFIC ESCALATION RULES:

FileValidationAgent:
- CRITICAL: validation_coverage < min_coverage threshold
- CRITICAL: processing_time_ms > max_processing_time_ms * 2
- WARNING: invalid_rows > 0 without clear error_messages

DataValidationAgent:
- CRITICAL: override_rate > max_override_rate threshold
- CRITICAL: Missing override_justifications for any overrides
- WARNING: rule_violations without proper documentation

SP_Parser_Agent:
- CRITICAL: parsing_confidence < min_rules_coverage threshold
- CRITICAL: Missing rules_json or column_template
- WARNING: processing_time_ms > max_processing_time_ms

ESCALATION DECISION:
Based on the findings and thresholds, determine:
- RECOMMENDED: Human intervention needed
- NOT_NEEDED: Agent output acceptable for next stage
- Provide clear reasoning for the decision
- Include specific issues that triggered escalation
- Suggest immediate actions if escalation recommended
