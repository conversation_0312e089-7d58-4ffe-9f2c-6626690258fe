{"success": true, "message": "Validation completed. See details.", "validation_details": {"column_presence_and_order": {"status": "PASS", "details": "All expected columns are present and in correct order."}, "data_types": {"status": "PASS", "details": [{"column": "UNIQUE_ID", "expected": "STRING", "actual": "STRING"}, {"column": "PORTFOLIO_ID", "expected": "STRING", "actual": "STRING"}, {"column": "REGISTERED_HOLDER", "expected": "STRING", "actual": "STRING"}, {"column": "PERIOD", "expected": "STRING", "actual": "STRING"}, {"column": "FUND_NAME", "expected": "STRING", "actual": "STRING"}, {"column": "NAV", "expected": "NUMBER", "actual": "NUMBER"}, {"column": "OWNERSHIP_PERCENTAGE", "expected": "NUMBER", "actual": "NUMBER"}, {"column": "CAPITAL_CALLED", "expected": "NUMBER", "actual": "NUMBER"}, {"column": "NO_OF_SHARES", "expected": "NUMBER", "actual": "NUMBER"}, {"column": "COMMITTED_CAPITAL", "expected": "NUMBER", "actual": "NUMBER"}]}, "required_constraints": {"status": "FAIL", "violations": [{"row": 3, "column": "OWNERSHIP_PERCENTAGE", "issue": "Null value found in non-nullable field."}]}}, "verdict": "INVALID", "logs": {"master_log": "logs/master.log", "rule_extraction_log": "logs/rule_extraction.log", "file_validation_log": "logs/file_validation.log"}}