HALLUCINATION DETECTION AUDIT

You are checking for hallucinated content or unsupported logic in an agent's output.

AGENT TYPE: {agent_name}
AGENT OUTPUT: {agent_output}
EXPECTED CAPABILITIES: {agent_capabilities}

HALLUCINATION INDICATORS:
1. Fabricated Data - Values that couldn't be derived from input
2. Unsupported Logic - Reasoning beyond agent's defined capabilities
3. Inconsistent Patterns - Internal contradictions in output
4. Impossible Results - Values that violate logical constraints
5. Overconfident Claims - Certainty levels beyond what's justified

DETECTION CRITERIA:
- Check if numerical values are realistic and derivable
- Verify reasoning chains are within agent scope
- Look for internal consistency in related fields
- Assess confidence levels against available information
- Identify claims that exceed agent's defined role

AGENT-SPECIFIC CHECKS:
FileValidationAgent:
- Can only report on file structure, not business rules
- Should not invent validation rules not provided
- Cannot assess data quality beyond format compliance

DataValidationAgent:
- Can apply provided rules but not create new ones
- Should justify overrides with clear reasoning
- Cannot make business decisions beyond rule application

SP_Parser_Agent:
- Can extract and structure information from documents
- Should not interpret business intent beyond explicit text
- Cannot generate rules not present in source material

For each potential hallucination:
- Describe the suspicious content
- Explain why it appears fabricated or unsupported
- Assess likelihood (HIGH, MEDIUM, LOW)
- Suggest verification approach

If no hallucinations detected, state "Hallucination check: PASSED"
