import openai
import yaml
import json
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
from typing import Dict, Any, Optional, List
from pathlib import Path

# Load environment variables from .env file
load_dotenv()

class GuardrailAgent:
    """
    AI-powered reasoning-based audit agent for validating outputs from autonomous validation agents.

    This agent is purely AI-driven with minimal hardcoding. It uses structured prompts and
    configuration-driven logic to audit agent outputs without redoing their work.
    """

    def __init__(self, api_key: Optional[str] = None):
        # Use provided API key or load from environment
        if api_key is None:
            api_key = os.getenv('OPENAI_API_KEY')

        if not api_key:
            raise ValueError("OpenAI API key must be provided either as parameter or OPENAI_API_KEY environment variable")

        self.client = openai.OpenAI(api_key=api_key)

        # Load configurations and prompts
        self._load_configurations()
        self._load_prompt_templates()

        # Ensure logging directories exist
        Path('data/audit_logs').mkdir(parents=True, exist_ok=True)
        Path('data/reports').mkdir(parents=True, exist_ok=True)

    def _load_configurations(self):
        """Load YAML configuration files"""
        with open('config/agent_profiles.yaml', 'r', encoding='utf-8') as f:
            self.agent_profiles = yaml.safe_load(f)
        with open('config/thresholds.yaml', 'r', encoding='utf-8') as f:
            self.thresholds = yaml.safe_load(f)

    def _load_prompt_templates(self):
        """Load prompt templates from ContextGuardrail directory"""
        context_dir = Path('ContextGuardrail')

        with open(context_dir / 'base_instruction_prompt.txt', 'r', encoding='utf-8') as f:
            self.base_instruction = f.read()
        with open(context_dir / 'schema_violation_prompt.txt', 'r', encoding='utf-8') as f:
            self.schema_violation_template = f.read()
        with open(context_dir / 'hallucination_check_prompt.txt', 'r', encoding='utf-8') as f:
            self.hallucination_template = f.read()
        with open(context_dir / 'escalation_policy_prompt.txt', 'r', encoding='utf-8') as f:
            self.escalation_template = f.read()

    def audit_agent_output(self, agent_name: str, agent_phase: str, agent_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main audit method - AI-powered with minimal hardcoding.

        Uses AI to analyze agent output against configurations and determine findings.
        """
        try:
            # Get agent configuration
            agent_config = self.agent_profiles.get(agent_name, {})

            # Create comprehensive audit prompt
            audit_prompt = f"""
            You are auditing the output of {agent_name} in {agent_phase}.

            AGENT CONFIGURATION:
            {json.dumps(agent_config, indent=2)}

            THRESHOLDS:
            {json.dumps(self.thresholds, indent=2)}

            AGENT OUTPUT TO AUDIT:
            {json.dumps(agent_output, indent=2)}

            Analyze this output and provide findings in this exact JSON format:
            {{
                "findings": ["finding 1", "finding 2", ...],
                "status": "PASSED" | "WARNING" | "CRITICAL",
                "escalation": "RECOMMENDED" | "NOT_NEEDED"
            }}

            Focus on:
            1. Schema compliance issues
            2. Threshold violations
            3. Hallucinated or inconsistent content
            4. Missing required fields or data quality issues

            Be concise and specific in findings.
            """

            # Get AI analysis
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.base_instruction},
                    {"role": "user", "content": audit_prompt}
                ],
                temperature=0.1
            )

            # Parse AI response
            ai_result = json.loads(response.choices[0].message.content)

            # Create final report
            report = {
                "agent": agent_name,
                "phase": agent_phase,
                "status": ai_result.get("status", "WARNING"),
                "findings": ai_result.get("findings", []),
                "escalation": ai_result.get("escalation", "NOT_NEEDED")
            }

            # Log the audit
            self._log_audit(report)

            return report

        except Exception as e:
            return {
                "agent": agent_name,
                "phase": agent_phase,
                "status": "CRITICAL",
                "findings": [f"Audit error: {str(e)}"],
                "escalation": "RECOMMENDED"
            }

    def _log_audit(self, report: Dict[str, Any]):
        """Simple logging for audit results"""
        timestamp = datetime.now(timezone.utc).isoformat()

        # JSON log for structured data
        json_log = {
            "timestamp": timestamp,
            "type": "audit_complete",
            "agent": report["agent"],
            "phase": report["phase"],
            "status": report["status"],
            "findings_count": len(report["findings"]),
            "escalation": report["escalation"]
        }

        # Human readable log
        status_marker = "[OK]" if report["status"] == "PASSED" else "[ESCALATE]" if report["escalation"] == "RECOMMENDED" else "[DONE]"
        human_log = f"{timestamp} {status_marker} Audit complete - {report['agent']} {report['status']} ({len(report['findings'])} findings)"

        # Write logs
        with open('data/audit_logs/audit_logs.jsonl', 'a', encoding='utf-8') as f:
            f.write(json.dumps(json_log, ensure_ascii=False) + '\n')

        with open('data/audit_logs/audit_human_readable.log', 'a', encoding='utf-8') as f:
            f.write(human_log + '\n')

        # Write full report
        report_filename = f"data/reports/audit_report_{report['agent']}_{timestamp.replace(':', '-')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)



