import openai
import yaml
import json
import os
from datetime import datetime
from dotenv import load_dotenv
from typing import Dict, Any, List, Optional
from pathlib import Path

# Load environment variables from .env file
load_dotenv()

class GuardrailAgent:
    """
    AI-powered reasoning-based audit agent for validating outputs from autonomous validation agents.

    This agent uses structured prompts and configuration-driven logic to audit agent outputs
    without redoing their work. It focuses on schema compliance, hallucination detection,
    threshold violations, and escalation assessment.
    """

    def __init__(self, api_key: Optional[str] = None):
        # Use provided API key or load from environment
        if api_key is None:
            api_key = os.getenv('OPENAI_API_KEY')

        if not api_key:
            raise ValueError("OpenAI API key must be provided either as parameter or OPENAI_API_KEY environment variable")

        self.client = openai.OpenAI(api_key=api_key)

        # Load all configurations
        self._load_configurations()

        # Load prompt templates
        self._load_prompt_templates()

        # Ensure logging directories exist
        self._ensure_logging_directories()

    def _load_configurations(self):
        """Load all YAML configuration files with error handling"""
        try:
            # Load agent profiles
            with open('config/agent_profiles.yaml', 'r', encoding='utf-8') as f:
                self.agent_profiles = yaml.safe_load(f)

            # Load thresholds
            with open('config/thresholds.yaml', 'r', encoding='utf-8') as f:
                self.thresholds = yaml.safe_load(f)

            # Load guardrail rules (for backward compatibility)
            with open('config/guardrail_rules.yaml', 'r', encoding='utf-8') as f:
                self.guardrail_rules = yaml.safe_load(f)

            # Validate configuration structure
            if not isinstance(self.agent_profiles, dict):
                raise ValueError("agent_profiles.yaml must contain a dictionary")
            if not isinstance(self.thresholds, dict):
                raise ValueError("thresholds.yaml must contain a dictionary")

        except FileNotFoundError as e:
            raise FileNotFoundError(f"Configuration file not found: {e}")
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML configuration: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")

    def _load_prompt_templates(self):
        """Load all prompt templates from ContextGuardrail directory"""
        try:
            context_dir = Path('ContextGuardrail')

            # Load base instruction prompt
            with open(context_dir / 'base_instruction_prompt.txt', 'r', encoding='utf-8') as f:
                self.base_instruction = f.read()

            # Load schema violation prompt
            with open(context_dir / 'schema_violation_prompt.txt', 'r', encoding='utf-8') as f:
                self.schema_violation_template = f.read()

            # Load hallucination check prompt
            with open(context_dir / 'hallucination_check_prompt.txt', 'r', encoding='utf-8') as f:
                self.hallucination_template = f.read()

            # Load escalation policy prompt
            with open(context_dir / 'escalation_policy_prompt.txt', 'r', encoding='utf-8') as f:
                self.escalation_template = f.read()

        except FileNotFoundError as e:
            raise FileNotFoundError(f"Prompt template file not found: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load prompt templates: {e}")

    def _ensure_logging_directories(self):
        """Ensure audit logging directories exist"""
        Path('data/audit_logs').mkdir(parents=True, exist_ok=True)
        Path('data/reports').mkdir(parents=True, exist_ok=True)

    def audit_agent_output(self, agent_name: str, agent_phase: str, agent_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main audit method that follows the specified input/output format.

        Args:
            agent_name: Name of the agent being audited (e.g., "FileValidationAgent")
            agent_phase: "Audit Phase 1" or "Audit Phase 2"
            agent_output: JSON output from the upstream agent

        Returns:
            Structured audit report in the specified JSON format
        """
        try:
            # Validate agent is known
            if agent_name not in self.agent_profiles:
                raise ValueError(f"Unknown agent type: {agent_name}")

            # Get expected schema and thresholds
            expected_schema = self.agent_profiles[agent_name]['expected_output_structure']
            threshold_config = self.thresholds

            # Log audit start
            self._log_audit_start(agent_name, agent_phase, agent_output)

            # Initialize findings list
            findings = []

            # 1. Schema Compliance Check
            self._log_step("Schema compliance check", agent_name)
            schema_findings = self._check_schema_compliance(agent_output, expected_schema, agent_name)
            findings.extend(schema_findings)

            # 2. Hallucination Detection
            self._log_step("Hallucination detection", agent_name)
            hallucination_findings = self._check_hallucinations(agent_output, agent_name)
            findings.extend(hallucination_findings)

            # 3. Threshold Violation Assessment
            self._log_step("Threshold violation assessment", agent_name)
            threshold_findings = self._check_threshold_violations(agent_output, threshold_config, agent_name)
            findings.extend(threshold_findings)

            # 4. Determine overall status
            status = self._determine_status(findings)

            # 5. Escalation Assessment
            escalation = self._assess_escalation(findings, agent_name, agent_phase, threshold_config)

            # Generate final report
            report = {
                "agent": agent_name,
                "phase": agent_phase,
                "status": status,
                "findings": findings,
                "escalation": escalation
            }

            # Log completion
            self._log_audit_complete(report)

            return report

        except Exception as e:
            # Handle errors gracefully
            error_report = {
                "agent": agent_name,
                "phase": agent_phase,
                "status": "CRITICAL",
                "findings": [f"Audit error: {str(e)}"],
                "escalation": "RECOMMENDED"
            }

            self._log_audit_error(agent_name, agent_phase, str(e))
            return error_report

    def _check_schema_compliance(self, agent_output: Dict[str, Any], expected_schema: Dict[str, Any], agent_name: str) -> List[str]:
        """
        Check agent output for schema compliance using AI reasoning.

        Args:
            agent_output: The actual agent output to validate
            expected_schema: Expected schema structure from agent_profiles.yaml
            agent_name: Name of the agent being audited

        Returns:
            List of schema violation findings
        """
        try:
            # Prepare the prompt with actual data
            prompt = self.schema_violation_template.format(
                expected_schema=json.dumps(expected_schema, indent=2),
                agent_output=json.dumps(agent_output, indent=2)
            )

            # Get AI analysis
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.base_instruction},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )

            analysis = response.choices[0].message.content.strip()

            # Parse findings from AI response
            findings = []
            if "Schema compliance: PASSED" not in analysis:
                # Extract specific violations from the analysis
                lines = analysis.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#') and 'Schema compliance:' not in line:
                        if any(keyword in line.lower() for keyword in ['missing', 'violation', 'error', 'invalid', 'unexpected']):
                            findings.append(f"Schema violation: {line}")

            return findings

        except Exception as e:
            return [f"Schema compliance check failed: {str(e)}"]

    def _check_hallucinations(self, agent_output: Dict[str, Any], agent_name: str) -> List[str]:
        """
        Check for hallucinated content or unsupported logic using AI reasoning.

        Args:
            agent_output: The actual agent output to analyze
            agent_name: Name of the agent being audited

        Returns:
            List of hallucination findings
        """
        try:
            # Get agent capabilities from profile
            agent_capabilities = self.agent_profiles[agent_name].get('capabilities', [])

            # Prepare the prompt
            prompt = self.hallucination_template.format(
                agent_name=agent_name,
                agent_output=json.dumps(agent_output, indent=2),
                agent_capabilities=json.dumps(agent_capabilities, indent=2)
            )

            # Get AI analysis
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.base_instruction},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )

            analysis = response.choices[0].message.content.strip()

            # Parse findings from AI response
            findings = []
            if "Hallucination check: PASSED" not in analysis:
                lines = analysis.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#') and 'Hallucination check:' not in line:
                        if any(keyword in line.lower() for keyword in ['fabricated', 'hallucination', 'unsupported', 'suspicious', 'inconsistent']):
                            findings.append(f"Hallucination detected: {line}")

            return findings

        except Exception as e:
            return [f"Hallucination check failed: {str(e)}"]

    def _check_threshold_violations(self, agent_output: Dict[str, Any], threshold_config: Dict[str, Any], agent_name: str) -> List[str]:
        """
        Check for threshold violations based on dynamic configuration.

        Args:
            agent_output: The actual agent output to analyze
            threshold_config: Threshold configuration from thresholds.yaml
            agent_name: Name of the agent being audited

        Returns:
            List of threshold violation findings
        """
        findings = []

        try:
            # Get agent-specific thresholds
            agent_thresholds = threshold_config.get('performance_thresholds', {}).get(agent_name, {})

            # Check processing time
            processing_time = agent_output.get('metadata', {}).get('processing_time_ms', 0)
            max_time = agent_thresholds.get('max_processing_time_ms', float('inf'))
            critical_time = agent_thresholds.get('max_processing_time_critical', float('inf'))

            if processing_time > critical_time:
                findings.append(f"Critical processing time violation: {processing_time}ms > {critical_time}ms")
            elif processing_time > max_time:
                findings.append(f"Processing time warning: {processing_time}ms > {max_time}ms")

            # Agent-specific threshold checks
            if agent_name == "FileValidationAgent":
                findings.extend(self._check_file_validation_thresholds(agent_output, agent_thresholds))
            elif agent_name == "DataValidationAgent":
                findings.extend(self._check_data_validation_thresholds(agent_output, agent_thresholds))
            elif agent_name == "SP_Parser_Agent":
                findings.extend(self._check_parser_thresholds(agent_output, agent_thresholds))

            return findings

        except Exception as e:
            return [f"Threshold check failed: {str(e)}"]

    def _check_file_validation_thresholds(self, agent_output: Dict[str, Any], thresholds: Dict[str, Any]) -> List[str]:
        """Check FileValidationAgent specific thresholds"""
        findings = []

        # Check validation coverage
        coverage = agent_output.get('validation_coverage', 0)
        min_coverage = thresholds.get('min_coverage', 0.98)
        if coverage < min_coverage:
            findings.append(f"Validation coverage below threshold: {coverage} < {min_coverage}")

        # Check invalid rows ratio
        valid_rows = agent_output.get('valid_rows', 0)
        invalid_rows = agent_output.get('invalid_rows', 0)
        total_rows = valid_rows + invalid_rows

        if total_rows > 0:
            invalid_ratio = invalid_rows / total_rows
            max_invalid_ratio = thresholds.get('max_invalid_rows_ratio', 0.02)
            if invalid_ratio > max_invalid_ratio:
                findings.append(f"Invalid rows ratio exceeds threshold: {invalid_ratio:.3f} > {max_invalid_ratio}")

        return findings

    def _check_data_validation_thresholds(self, agent_output: Dict[str, Any], thresholds: Dict[str, Any]) -> List[str]:
        """Check DataValidationAgent specific thresholds"""
        findings = []

        # Check override rate
        override_rate = agent_output.get('override_rate', 0)
        max_override_rate = thresholds.get('max_override_rate', 0.05)
        if override_rate > max_override_rate:
            findings.append(f"Override rate exceeds threshold: {override_rate} > {max_override_rate}")

        # Check override justifications
        overrides = agent_output.get('override_justifications', [])
        min_justification_length = thresholds.get('min_justification_length', 10)
        for i, justification in enumerate(overrides):
            if len(justification) < min_justification_length:
                findings.append(f"Override justification {i+1} too short: {len(justification)} < {min_justification_length} chars")

        return findings

    def _check_parser_thresholds(self, agent_output: Dict[str, Any], thresholds: Dict[str, Any]) -> List[str]:
        """Check SP_Parser_Agent specific thresholds"""
        findings = []

        # Check parsing confidence
        confidence = agent_output.get('parsing_confidence', 0)
        min_confidence = thresholds.get('min_parsing_confidence', 0.90)
        if confidence < min_confidence:
            findings.append(f"Parsing confidence below threshold: {confidence} < {min_confidence}")

        # Check rules extraction
        rules_json = agent_output.get('rules_json', {})
        if isinstance(rules_json, dict):
            rules_count = len(rules_json)
            min_rules = thresholds.get('min_rules_extracted', 5)
            if rules_count < min_rules:
                findings.append(f"Insufficient rules extracted: {rules_count} < {min_rules}")

        return findings

    def _determine_status(self, findings: List[str]) -> str:
        """
        Determine overall audit status based on findings.

        Args:
            findings: List of all findings from various checks

        Returns:
            Status string: "PASSED", "WARNING", or "CRITICAL"
        """
        if not findings:
            return "PASSED"

        # Check for critical indicators
        critical_keywords = ['critical', 'security', 'hallucination', 'schema violation', 'exceeds threshold']
        for finding in findings:
            if any(keyword in finding.lower() for keyword in critical_keywords):
                return "CRITICAL"

        # If no critical issues, but findings exist, it's a warning
        return "WARNING"

    def _assess_escalation(self, findings: List[str], agent_name: str, agent_phase: str, threshold_config: Dict[str, Any]) -> Optional[str]:
        """
        Assess whether escalation is needed using AI reasoning.

        Args:
            findings: List of audit findings
            agent_name: Name of the agent being audited
            agent_phase: Current audit phase
            threshold_config: Threshold configuration

        Returns:
            Escalation recommendation: "RECOMMENDED", "NOT_NEEDED", or None
        """
        try:
            # Get escalation thresholds
            escalation_thresholds = threshold_config.get('escalation_thresholds', {})

            # Prepare the prompt
            prompt = self.escalation_template.format(
                agent_name=agent_name,
                agent_phase=agent_phase,
                findings=json.dumps(findings, indent=2),
                escalation_thresholds=json.dumps(escalation_thresholds, indent=2)
            )

            # Get AI analysis
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.base_instruction},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            analysis = response.choices[0].message.content.strip().lower()

            # Parse escalation decision
            if "recommended" in analysis or "escalation: recommended" in analysis:
                return "RECOMMENDED"
            elif "not_needed" in analysis or "not needed" in analysis:
                return "NOT_NEEDED"
            else:
                # Fallback logic based on findings severity
                critical_count = sum(1 for f in findings if 'critical' in f.lower())
                warning_count = len(findings) - critical_count

                if critical_count >= escalation_thresholds.get('critical_issues', 1):
                    return "RECOMMENDED"
                elif warning_count >= escalation_thresholds.get('warning_issues', 3):
                    return "RECOMMENDED"
                else:
                    return "NOT_NEEDED"

        except Exception as e:
            # If escalation assessment fails, err on the side of caution
            return "RECOMMENDED" if findings else "NOT_NEEDED"

    def _log_audit_start(self, agent_name: str, agent_phase: str, agent_output: Dict[str, Any]):
        """Log the start of an audit with structured and human-readable formats"""
        timestamp = datetime.utcnow().isoformat()

        # Structured JSON log
        json_log = {
            "timestamp": timestamp,
            "event": "audit_start",
            "agent": agent_name,
            "phase": agent_phase,
            "task_id": agent_output.get('metadata', {}).get('task_id', 'unknown')
        }

        # Human-readable log
        human_log = f"{timestamp} [START] GuardrailAgent audit of {agent_name} - {agent_phase}"

        self._write_logs(json_log, human_log)

    def _log_step(self, step_name: str, agent_name: str):
        """Log an audit step"""
        timestamp = datetime.utcnow().isoformat()

        # Structured JSON log
        json_log = {
            "timestamp": timestamp,
            "event": "audit_step",
            "step": step_name,
            "agent": agent_name
        }

        # Human-readable log
        human_log = f"{timestamp} [STEP] {step_name} for {agent_name}"

        self._write_logs(json_log, human_log)

    def _log_audit_complete(self, report: Dict[str, Any]):
        """Log the completion of an audit"""
        timestamp = datetime.utcnow().isoformat()

        # Structured JSON log
        json_log = {
            "timestamp": timestamp,
            "event": "audit_complete",
            "agent": report["agent"],
            "phase": report["phase"],
            "status": report["status"],
            "findings_count": len(report["findings"]),
            "escalation": report["escalation"]
        }

        # Human-readable log
        status_marker = "[OK]" if report["status"] == "PASSED" else "[ESCALATE]" if report["escalation"] == "RECOMMENDED" else "[DONE]"
        human_log = f"{timestamp} {status_marker} Audit complete - {report['agent']} {report['status']} ({len(report['findings'])} findings)"

        self._write_logs(json_log, human_log)

        # Write full report to reports directory
        report_filename = f"data/reports/audit_report_{report['agent']}_{timestamp.replace(':', '-')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

    def _log_audit_error(self, agent_name: str, agent_phase: str, error: str):
        """Log an audit error"""
        timestamp = datetime.utcnow().isoformat()

        # Structured JSON log
        json_log = {
            "timestamp": timestamp,
            "event": "audit_error",
            "agent": agent_name,
            "phase": agent_phase,
            "error": error
        }

        # Human-readable log
        human_log = f"{timestamp} [ERROR] Audit failed for {agent_name} - {error}"

        self._write_logs(json_log, human_log)

    def _write_logs(self, json_log: Dict[str, Any], human_log: str):
        """Write both structured and human-readable logs"""
        # Write JSON log
        with open('data/audit_logs/audit_logs.jsonl', 'a', encoding='utf-8') as f:
            f.write(json.dumps(json_log, ensure_ascii=False) + '\n')

        # Write human-readable log
        with open('data/audit_logs/audit_human_readable.log', 'a', encoding='utf-8') as f:
            f.write(human_log + '\n')