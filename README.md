# CBRE GuardrailAgent - AI-Powered Audit Agent

## Overview

The GuardrailAgent is a reasoning-based audit agent in an AI-powered agentic validation pipeline. It audits outputs from autonomous validation agents (FileValidationAgent, DataValidationAgent) **without redoing their work**, using structured prompts and configuration-driven logic.

### Key Principles
- **AI-Powered Reasoning**: Uses OpenAI GPT-4 for intelligent audit analysis
- **Configuration-Driven**: All validation logic loaded from YAML configurations
- **Structured Prompts**: Uses templates from `ContextGuardrail/` for consistent analysis
- **No Hardcoding**: Dynamic thresholds, schema validation, and escalation logic
- **Dual Logging**: Both structured JSON logs and human-readable logs

### Agent Chain Context
```
          FileValidationAgent ─▶ file_validation_output.json
                                │
                                ▼
     🛡️ GuardrailAgent (Audit Phase 1: FileValidationAgent)
                                │
                            [PASS] ─────────────┐
                                │               │
                            [FAIL]              ▼
                            └────── escalate/log │
                                               ▼
          DataValidationAgent ─▶ data_validation_output.json
                                │
                                ▼
     🛡️ GuardrailAgent (Audit Phase 2: DataValidationAgent)
                                │
                            [PASS] ──────▶ Final report
                            [FAIL] ──────▶ Escalation + stop
```

## Project Structure

```
CBRE_Guardrail_Agent/
├── agents/                          # AI-powered GuardrailAgent
│   ├── __init__.py
│   └── guardrail_agent.py          # Main GuardrailAgent implementation
├── ContextGuardrail/               # Structured prompt templates
│   ├── base_instruction_prompt.txt
│   ├── schema_violation_prompt.txt
│   ├── hallucination_check_prompt.txt
│   └── escalation_policy_prompt.txt
├── config/                         # Configuration files
│   ├── agent_profiles.yaml        # Agent schemas and capabilities
│   └── thresholds.yaml            # Dynamic threshold configurations
├── data/                          # Logging and reports
│   ├── audit_logs/               # Structured and human-readable logs
│   └── reports/                  # Audit reports
├── test_data/                    # Sample agent outputs for testing
│   ├── file_validation_output.json
│   ├── data_validation_output.json
│   └── parser_output_output.json
├── test_guardrail_agent.py       # Test suite
├── run_guardrail_production.py   # Production runner
├── .env.template                 # Environment setup template
└── requirements.txt              # Dependencies
```
## Getting Started

### Prerequisites
- Python 3.8+
- OpenAI API key

### Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**:
   ```bash
   cp .env.template .env
   # Edit .env and add your OPENAI_API_KEY
   ```

### Usage

#### Production Usage
```bash
python run_guardrail_production.py FileValidationAgent "Audit Phase 1" test_data/file_validation_output.json
python run_guardrail_production.py DataValidationAgent "Audit Phase 2" test_data/data_validation_output.json
```

#### Testing
```bash
python test_guardrail_agent.py
```

#### Programmatic Usage
```python
from agents.guardrail_agent import GuardrailAgent

guardrail = GuardrailAgent()
result = guardrail.audit_agent_output(
    agent_name="FileValidationAgent",
    agent_phase="Audit Phase 1",
    agent_output=agent_output_data
)
```
## Input/Output Format

### Input Format
```python
guardrail.audit_agent_output(
    agent_name="FileValidationAgent",           # Name of agent being audited
    agent_phase="Audit Phase 1",               # "Audit Phase 1" or "Audit Phase 2"
    agent_output=agent_output_dict              # JSON output from upstream agent
)
```

### Output Format
```json
{
  "agent": "FileValidationAgent",
  "phase": "Audit Phase 1",
  "status": "PASSED" | "WARNING" | "CRITICAL",
  "findings": [
    "Short explanation: e.g., 'Column NAV missing from output'",
    "Violation: Invalid flag detected on row 4 – exceeds threshold",
    "Hallucination: Detected reasoning inconsistent with input schema"
  ],
  "escalation": "RECOMMENDED" | "NOT_NEEDED" | null
}
```
## Audit Process

The GuardrailAgent performs four key audit phases:

1. **Schema Compliance Check** - Verify output matches expected structure
2. **Hallucination Detection** - Check for unsupported logic or fabricated content
3. **Threshold Violation Assessment** - Compare against dynamic thresholds
4. **Escalation Assessment** - Determine if human intervention needed

## Configuration

### `config/agent_profiles.yaml`
Complete expected schemas for each agent with capabilities and limitations:

```yaml
FileValidationAgent:
  min_coverage: 0.98
  max_processing_time_ms: 3000
  capabilities:
    - "File structure validation"
    - "Format compliance checking"
  expected_output_structure:
    metadata:
      required: true
      type: "object"
    file_name:
      type: "string"
      required: true
    # ... complete schema definition
```

### `config/thresholds.yaml`
Dynamic threshold configurations:

```yaml
escalation_thresholds:
  critical_issues: 1
  warning_issues: 3

performance_thresholds:
  FileValidationAgent:
    min_coverage: 0.98
    max_processing_time_ms: 3000
  # ... agent-specific thresholds
```

## Logging

The GuardrailAgent implements dual logging:

- **Structured JSON Logs**: `data/audit_logs/audit_logs.jsonl`
- **Human-Readable Logs**: `data/audit_logs/audit_human_readable.log`
- **Audit Reports**: `data/reports/audit_report_*.json`

## Agent Chain Context

The GuardrailAgent is strictly part of a sequential audit chain and does NOT:
- Recalculate or re-validate file or business logic directly
- Hardcode key names, thresholds, column rules, or agent behaviors
- Block or mask sensitive data (all data is visible during auditing)

It ONLY audits and explains the upstream agent's output based on configuration-driven logic.