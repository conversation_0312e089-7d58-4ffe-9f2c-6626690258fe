@echo off
REM Windows batch script to run GuardrailAgent
REM Usage: run_guardrail.bat <agent_name> <agent_phase> <agent_output_file>

if "%~3"=="" (
    echo Usage: run_guardrail.bat ^<agent_name^> ^<agent_phase^> ^<agent_output_file^>
    echo.
    echo Examples:
    echo   run_guardrail.bat FileValidationAgent "Audit Phase 1" test_data\file_validation_output.json
    echo   run_guardrail.bat DataValidationAgent "Audit Phase 2" test_data\data_validation_output.json
    echo   run_guardrail.bat SP_Parser_Agent "Audit Phase 1" test_data\parser_output_output.json
    exit /b 1
)

python run_guardrail_production.py %1 %2 %3
