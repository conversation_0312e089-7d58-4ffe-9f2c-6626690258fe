SCHEMA COMPLIANCE AUDIT

You are auditing an agent's output for schema compliance. Compare the actual output against the expected schema structure.

AUDIT CRITERIA:
1. Required Fields - All expected fields must be present
2. Data Types - Fields must match expected data types
3. Structure Integrity - Nested objects and arrays must follow expected format
4. Value Constraints - Check for null/empty values where not allowed

EXPECTED SCHEMA STRUCTURE:
{expected_schema}

ACTUAL AGENT OUTPUT:
{agent_output}

ANALYSIS INSTRUCTIONS:
- Identify missing required fields
- Check for unexpected additional fields
- Verify data type consistency
- Assess structural completeness
- Note any format violations

For each violation found, provide:
- Field name or path
- Expected vs actual structure/type
- Severity level (CRITICAL, WARNING, INFO)
- Brief explanation of the issue

If no violations found, state "Schema compliance: PASSED"

Focus on structural integrity, not business logic validation.
