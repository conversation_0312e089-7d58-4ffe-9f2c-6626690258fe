#!/usr/bin/env python3
"""
Dynamic Agent Profile Generator

This script generates agent_profiles.yaml dynamically based on SP_Parser_Agent outputs:
- schema_template.json (column structure and data types)
- data_rules.json (validation rules and constraints)
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, List

def load_sp_parser_outputs():
    """Load SP_Parser_Agent output files"""
    with open('test_data/schema_template.json', 'r') as f:
        schema_template = json.load(f)
    
    with open('test_data/data_rules.json', 'r') as f:
        data_rules = json.load(f)
    
    return schema_template, data_rules

def extract_column_info(schema_template: List[Dict]) -> Dict[str, str]:
    """Extract column names and infer data types from schema template"""
    columns = {}
    
    # Get all unique columns from schema template
    for rule in schema_template:
        if rule.get('columns'):
            for column in rule['columns']:
                # Infer data type based on column name patterns
                if column in ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']:
                    columns[column] = 'number'
                else:
                    columns[column] = 'string'
    
    return columns

def extract_constraints(data_rules: List[Dict], column: str) -> List[Dict[str, Any]]:
    """Extract constraints for a specific column from data rules"""
    constraints = []
    
    for rule in data_rules:
        if column in rule.get('columns', []):
            rule_type = rule.get('type')
            rule_text = rule.get('rule', '')
            
            if rule_type == 'null_check':
                constraints.append({'not_null': True})
            elif rule_type == 'duplicate_check':
                constraints.append({'unique': True})
            elif rule_type == 'format_check':
                if 'alphanumeric' in rule_text.lower():
                    constraints.append({'pattern': '^[a-zA-Z0-9]+$'})
                elif 'not start with' in rule_text.lower() and 'TOTAL' in rule_text:
                    constraints.append({'not_starts_with': 'TOTAL'})
    
    return constraints

def generate_file_validation_agent_profile(columns: Dict[str, str], data_rules: List[Dict]) -> Dict[str, Any]:
    """Generate FileValidationAgent profile"""
    properties = {}
    required_fields = []
    
    for column, data_type in columns.items():
        properties[column] = {
            'type': data_type
        }
        
        # Add constraints
        constraints = extract_constraints(data_rules, column)
        if constraints:
            properties[column]['constraints'] = constraints
        
        # All columns are required based on schema template
        required_fields.append(column)
    
    return {
        'FileValidationAgent': {
            'max_processing_time_ms': 5000,
            'min_coverage': 0.98,
            'capabilities': [
                'File structure validation',
                'Column presence verification',
                'Data type validation',
                'Basic format checking'
            ],
            'limitations': [
                'Cannot validate business logic',
                'Cannot check cross-record relationships',
                'Cannot validate data quality beyond format'
            ],
            'expected_output_structure': {
                'metadata': {
                    'required': True,
                    'type': 'object',
                    'fields': {
                        'agent_type': {
                            'type': 'string',
                            'required': True,
                            'expected_value': 'FileValidationAgent'
                        },
                        'agent_version': {
                            'type': 'string',
                            'required': True
                        },
                        'processing_time_ms': {
                            'type': 'integer',
                            'required': True,
                            'min_value': 0
                        },
                        'task_id': {
                            'type': 'string',
                            'required': True
                        },
                        'timestamp': {
                            'type': 'string',
                            'required': True,
                            'format': 'ISO8601'
                        }
                    }
                },
                'validation_coverage': {
                    'type': 'number',
                    'required': True,
                    'min_value': 0.0,
                    'max_value': 1.0
                },
                'valid_rows': {
                    'type': 'integer',
                    'required': True,
                    'min_value': 0
                },
                'invalid_rows': {
                    'type': 'integer',
                    'required': True,
                    'min_value': 0
                },
                'column_validation': {
                    'type': 'object',
                    'required': True,
                    'properties': properties
                }
            }
        }
    }

def generate_data_validation_agent_profile(columns: Dict[str, str], data_rules: List[Dict]) -> Dict[str, Any]:
    """Generate DataValidationAgent profile"""
    properties = {}
    required_fields = []
    
    for column, data_type in columns.items():
        properties[column] = {
            'type': data_type
        }
        
        # Add constraints
        constraints = extract_constraints(data_rules, column)
        if constraints:
            properties[column]['constraints'] = constraints
        
        required_fields.append(column)
    
    return {
        'DataValidationAgent': {
            'max_processing_time_ms': 10000,
            'max_override_rate': 0.05,
            'capabilities': [
                'Business rule validation',
                'Cross-record validation',
                'Data quality assessment',
                'Override management'
            ],
            'limitations': [
                'Cannot modify source data',
                'Cannot validate external references',
                'Cannot perform complex calculations'
            ],
            'expected_output_structure': {
                'metadata': {
                    'required': True,
                    'type': 'object',
                    'fields': {
                        'agent_type': {
                            'type': 'string',
                            'required': True,
                            'expected_value': 'DataValidationAgent'
                        },
                        'agent_version': {
                            'type': 'string',
                            'required': True
                        },
                        'processing_time_ms': {
                            'type': 'integer',
                            'required': True,
                            'min_value': 0
                        },
                        'task_id': {
                            'type': 'string',
                            'required': True
                        },
                        'timestamp': {
                            'type': 'string',
                            'required': True,
                            'format': 'ISO8601'
                        }
                    }
                },
                'validation_results': {
                    'type': 'array',
                    'required': True,
                    'items': {
                        'type': 'object',
                        'required': required_fields,
                        'properties': properties
                    }
                },
                'override_rate': {
                    'type': 'number',
                    'required': True,
                    'min_value': 0.0,
                    'max_value': 1.0
                },
                'override_justifications': {
                    'type': 'array',
                    'required': True,
                    'items': {
                        'type': 'string'
                    }
                }
            }
        }
    }

def main():
    """Main function to generate agent profiles dynamically"""
    print("🔄 Loading SP_Parser_Agent outputs...")
    schema_template, data_rules = load_sp_parser_outputs()
    
    print("📊 Extracting column information...")
    columns = extract_column_info(schema_template)
    print(f"   Found {len(columns)} columns: {list(columns.keys())}")
    
    print("🏗️  Generating FileValidationAgent profile...")
    file_validation_profile = generate_file_validation_agent_profile(columns, data_rules)
    
    print("🏗️  Generating DataValidationAgent profile...")
    data_validation_profile = generate_data_validation_agent_profile(columns, data_rules)
    
    # Combine profiles
    agent_profiles = {}
    agent_profiles.update(file_validation_profile)
    agent_profiles.update(data_validation_profile)
    
    print("💾 Writing agent_profiles.yaml...")
    with open('config/agent_profiles.yaml', 'w') as f:
        yaml.dump(agent_profiles, f, default_flow_style=False, indent=2, sort_keys=False)
    
    print("✅ Successfully generated config/agent_profiles.yaml")
    print(f"   - FileValidationAgent: {len(file_validation_profile['FileValidationAgent']['expected_output_structure']['column_validation']['properties'])} columns")
    print(f"   - DataValidationAgent: {len(data_validation_profile['DataValidationAgent']['expected_output_structure']['validation_results']['items']['properties'])} columns")

if __name__ == "__main__":
    main()
