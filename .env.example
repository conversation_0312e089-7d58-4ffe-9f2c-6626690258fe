# Environment configuration for CBRE Guardrail Agent
# Copy this file to .env and fill in your actual values

# OpenAI API Configuration (REQUIRED)
OPENAI_API_KEY=sk-your-actual-openai-api-key-here

# AI Model Configuration (GPT-4o-mini is cost-effective and fast)
OPENAI_MODEL=gpt-4o-mini

# Optional: Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Optional: Agent Configuration
AUDIT_TIMEOUT_SECONDS=30
MAX_RETRIES=3
