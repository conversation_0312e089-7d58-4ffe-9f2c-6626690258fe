#!/usr/bin/env python3
"""
Quick script to update agent profiles from SP_Parser_Agent outputs
"""

import subprocess
import sys
from pathlib import Path

def main():
    """Update agent profiles and test the system"""
    print("🔄 Updating agent profiles from SP_Parser_Agent outputs...")
    
    # Check if required files exist
    required_files = [
        'test_data/schema_template.json',
        'test_data/data_rules.json'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ Error: Required file not found: {file_path}")
            print("   Please ensure SP_Parser_Agent outputs are in test_data/")
            return 1
    
    # Run the profile generator
    try:
        result = subprocess.run([sys.executable, 'generate_agent_profiles.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Agent profiles updated successfully!")
            print("\n📋 Generated profiles:")
            print(result.stdout)
            
            # Ask if user wants to test
            test_choice = input("\n🧪 Would you like to run tests with updated profiles? (y/n): ")
            if test_choice.lower() in ['y', 'yes']:
                print("\n🚀 Running tests...")
                test_result = subprocess.run([sys.executable, 'test_guardrail_agent.py'])
                return test_result.returncode
            else:
                print("✅ Profiles updated. You can run tests later with: python test_guardrail_agent.py")
                return 0
        else:
            print("❌ Error updating profiles:")
            print(result.stderr)
            return 1
            
    except Exception as e:
        print(f"❌ Error running profile generator: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
