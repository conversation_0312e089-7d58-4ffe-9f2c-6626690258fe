#!/bin/bash
# Shell script to run GuardrailAgent
# Usage: ./run_guardrail.sh <agent_name> <agent_phase> <agent_output_file>

if [ $# -ne 3 ]; then
    echo "Usage: $0 <agent_name> <agent_phase> <agent_output_file>"
    echo ""
    echo "Examples:"
    echo "  $0 FileValidationAgent \"Audit Phase 1\" test_data/file_validation_output.json"
    echo "  $0 DataValidationAgent \"Audit Phase 2\" test_data/data_validation_output.json"
    echo "  $0 SP_Parser_Agent \"Audit Phase 1\" test_data/parser_output_output.json"
    exit 1
fi

python run_guardrail_production.py "$1" "$2" "$3"
