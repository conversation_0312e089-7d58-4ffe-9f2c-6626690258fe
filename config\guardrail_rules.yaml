# Guardrail rules configuration (for backward compatibility)
# This file is loaded for backward compatibility but the main logic
# is now driven by agent_profiles.yaml and thresholds.yaml

# Legacy guardrail rules - kept for compatibility
legacy_rules:
  enabled: false
  
# Note: All active guardrail logic is now in:
# - agent_profiles.yaml (agent schemas and capabilities)
# - thresholds.yaml (dynamic threshold configurations)
