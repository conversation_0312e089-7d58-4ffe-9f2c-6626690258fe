# Agent profiles with complete schema definitions and validation criteria

FileValidationAgent:
  # Performance thresholds
  min_coverage: 0.98
  max_processing_time_ms: 3000

  # Agent capabilities and limitations
  capabilities:
    - "File structure validation"
    - "Format compliance checking"
    - "Row-level validation reporting"
    - "Basic data type verification"

  limitations:
    - "Cannot validate business rules"
    - "Cannot assess data quality beyond format"
    - "Cannot make business decisions"

  # Complete expected output schema
  expected_output_structure:
    metadata:
      required: true
      type: "object"
      fields:
        agent_type:
          type: "string"
          required: true
          expected_value: "FileValidationAgent"
        agent_version:
          type: "string"
          required: true
        processing_time_ms:
          type: "integer"
          required: true
          min_value: 0
        task_id:
          type: "string"
          required: true
        timestamp:
          type: "string"
          required: true
          format: "ISO8601"

    file_name:
      type: "string"
      required: true
      min_length: 1

    valid_rows:
      type: "integer"
      required: true
      min_value: 0

    invalid_rows:
      type: "integer"
      required: true
      min_value: 0

    error_messages:
      type: "array"
      required: true
      item_type: "string"

    validation_coverage:
      type: "number"
      required: true
      min_value: 0.0
      max_value: 1.0

DataValidationAgent:
  # Performance thresholds
  max_override_rate: 0.05
  max_processing_time_ms: 10000

  # Agent capabilities and limitations
  capabilities:
    - "Rule-based data validation"
    - "Override justification generation"
    - "Rule violation detection"
    - "Data quality assessment"

  limitations:
    - "Cannot create new validation rules"
    - "Cannot make business decisions beyond rule application"
    - "Cannot modify source data"

  # Complete expected output schema
  expected_output_structure:
    metadata:
      required: true
      type: "object"
      fields:
        agent_type:
          type: "string"
          required: true
          expected_value: "DataValidationAgent"
        agent_version:
          type: "string"
          required: true
        processing_time_ms:
          type: "integer"
          required: true
          min_value: 0
        task_id:
          type: "string"
          required: true
        timestamp:
          type: "string"
          required: true
          format: "ISO8601"

    validated_dataset:
      type: "object"
      required: true
      fields:
        record_count:
          type: "integer"
          required: true
          min_value: 0
        valid_records:
          type: "integer"
          required: true
          min_value: 0

    override_justifications:
      type: "array"
      required: true
      item_type: "string"

    override_rate:
      type: "number"
      required: true
      min_value: 0.0
      max_value: 1.0

    rule_violations:
      type: "array"
      required: true
      item_type: "object"

SP_Parser_Agent:
  # Performance thresholds
  max_processing_time_ms: 8000
  min_rules_coverage: 0.95

  # Agent capabilities and limitations
  capabilities:
    - "Document parsing and extraction"
    - "Rule structure identification"
    - "Column template generation"
    - "Confidence assessment"

  limitations:
    - "Cannot interpret business intent beyond explicit text"
    - "Cannot generate rules not present in source"
    - "Cannot validate extracted rules"

  # Complete expected output schema
  expected_output_structure:
    metadata:
      required: true
      type: "object"
      fields:
        agent_type:
          type: "string"
          required: true
          expected_value: "SP_Parser_Agent"
        agent_version:
          type: "string"
          required: true
        processing_time_ms:
          type: "integer"
          required: true
          min_value: 0
        task_id:
          type: "string"
          required: true
        timestamp:
          type: "string"
          required: true
          format: "ISO8601"

    rules_json:
      type: "object"
      required: true

    column_template:
      type: "object"
      required: true

    parsing_confidence:
      type: "number"
      required: true
      min_value: 0.0
      max_value: 1.0