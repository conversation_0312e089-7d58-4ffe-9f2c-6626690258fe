FileValidationAgent:
  max_processing_time_ms: 5000
  min_coverage: 0.98
  capabilities:
  - File structure validation
  - Column presence verification
  - Data type validation
  - Basic format checking
  limitations:
  - Cannot validate business logic
  - Cannot check cross-record relationships
  - Cannot validate data quality beyond format
  expected_output_structure:
    metadata:
      required: true
      type: object
      fields:
        agent_type:
          type: string
          required: true
          expected_value: FileValidationAgent
        agent_version:
          type: string
          required: true
        processing_time_ms:
          type: integer
          required: true
          min_value: 0
        task_id:
          type: string
          required: true
        timestamp:
          type: string
          required: true
          format: ISO8601
    validation_coverage:
      type: number
      required: true
      min_value: 0.0
      max_value: 1.0
    valid_rows:
      type: integer
      required: true
      min_value: 0
    invalid_rows:
      type: integer
      required: true
      min_value: 0
    column_validation:
      type: object
      required: true
      properties:
        UNIQUE_ID:
          type: string
          constraints:
          - not_starts_with: TOTAL
          - pattern: ^[a-zA-Z0-9]+$
          - not_null: true
          - unique: true
        PORTFOLIO_ID:
          type: string
        REGISTERED_HOLDER:
          type: string
        NAV:
          type: number
          constraints:
          - not_null: true
        OWNERSHIP_PERCENTAGE:
          type: number
          constraints:
          - not_null: true
        CAPITAL_CALLED:
          type: number
          constraints:
          - not_null: true
        NO_OF_SHARES:
          type: number
          constraints:
          - not_null: true
        COMMITTED_CAPITAL:
          type: number
          constraints:
          - not_null: true
        PERIOD:
          type: string
        FUND_NAME:
          type: string
DataValidationAgent:
  max_processing_time_ms: 10000
  max_override_rate: 0.05
  capabilities:
  - Business rule validation
  - Cross-record validation
  - Data quality assessment
  - Override management
  limitations:
  - Cannot modify source data
  - Cannot validate external references
  - Cannot perform complex calculations
  expected_output_structure:
    metadata:
      required: true
      type: object
      fields:
        agent_type:
          type: string
          required: true
          expected_value: DataValidationAgent
        agent_version:
          type: string
          required: true
        processing_time_ms:
          type: integer
          required: true
          min_value: 0
        task_id:
          type: string
          required: true
        timestamp:
          type: string
          required: true
          format: ISO8601
    validation_results:
      type: array
      required: true
      items:
        type: object
        required:
        - UNIQUE_ID
        - PORTFOLIO_ID
        - REGISTERED_HOLDER
        - NAV
        - OWNERSHIP_PERCENTAGE
        - CAPITAL_CALLED
        - NO_OF_SHARES
        - COMMITTED_CAPITAL
        - PERIOD
        - FUND_NAME
        properties:
          UNIQUE_ID:
            type: string
            constraints:
            - not_starts_with: TOTAL
            - pattern: ^[a-zA-Z0-9]+$
            - not_null: true
            - unique: true
          PORTFOLIO_ID:
            type: string
          REGISTERED_HOLDER:
            type: string
          NAV:
            type: number
            constraints:
            - not_null: true
          OWNERSHIP_PERCENTAGE:
            type: number
            constraints:
            - not_null: true
          CAPITAL_CALLED:
            type: number
            constraints:
            - not_null: true
          NO_OF_SHARES:
            type: number
            constraints:
            - not_null: true
          COMMITTED_CAPITAL:
            type: number
            constraints:
            - not_null: true
          PERIOD:
            type: string
          FUND_NAME:
            type: string
    override_rate:
      type: number
      required: true
      min_value: 0.0
      max_value: 1.0
    override_justifications:
      type: array
      required: true
      items:
        type: string
