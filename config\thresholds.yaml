# Dynamic threshold configurations for GuardrailAgent auditing

# Global escalation thresholds
escalation_thresholds:
  critical_issues: 1      # Number of critical issues to trigger escalation
  warning_issues: 3       # Number of warning issues to trigger escalation
  security_violations: 0  # Any security violation triggers immediate escalation

# Agent-specific performance thresholds
performance_thresholds:
  FileValidationAgent:
    min_coverage: 0.98                    # Minimum validation coverage required
    max_processing_time_ms: 3000          # Maximum acceptable processing time
    max_processing_time_critical: 6000    # Critical threshold (2x normal)
    max_invalid_rows_ratio: 0.02          # Maximum ratio of invalid rows
    
  DataValidationAgent:
    max_override_rate: 0.05               # Maximum acceptable override rate
    max_processing_time_ms: 10000         # Maximum acceptable processing time
    max_processing_time_critical: 20000   # Critical threshold (2x normal)
    min_justification_length: 10          # Minimum characters in override justifications
    max_rule_violations_ratio: 0.01       # Maximum ratio of rule violations

# Schema validation thresholds
schema_thresholds:
  missing_required_fields: 0      # Any missing required field is critical
  unexpected_fields_warning: 3    # Number of unexpected fields to trigger warning
  type_mismatches: 0              # Any type mismatch is critical
  null_critical_fields: 0         # Any null in critical field is critical

# Hallucination detection thresholds
hallucination_thresholds:
  confidence_levels:
    high_confidence_threshold: 0.8    # Above this = high confidence hallucination
    medium_confidence_threshold: 0.5  # Above this = medium confidence hallucination
  
  pattern_detection:
    fabricated_data_indicators: 2     # Number of indicators to flag as fabricated
    logic_inconsistency_threshold: 1  # Number of inconsistencies to flag
    overconfidence_threshold: 0.95    # Confidence level that seems too high

# Security and data leakage thresholds
security_thresholds:
  sensitive_pattern_matches: 0    # Any match triggers immediate escalation
  data_exposure_risk: 0           # Any exposure risk triggers escalation
  
  pattern_confidence:
    ssn_pattern_confidence: 0.9
    credit_card_confidence: 0.8
    email_confidence: 0.7

# Business logic validation thresholds (agent-agnostic)
business_logic_thresholds:
  data_quality:
    completeness_threshold: 0.95      # Minimum data completeness
    consistency_threshold: 0.98       # Minimum data consistency
    accuracy_threshold: 0.99          # Minimum data accuracy
    
  processing_efficiency:
    memory_usage_mb: 512              # Maximum memory usage
    cpu_utilization_percent: 80       # Maximum CPU utilization
    
  output_quality:
    min_output_completeness: 0.95     # Minimum output completeness
    max_error_rate: 0.01              # Maximum acceptable error rate
