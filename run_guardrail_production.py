#!/usr/bin/env python3
"""
Production runner for the GuardrailAgent.

This script demonstrates how to use the GuardrailAgent in a production environment
following the exact input/output format specified in the requirements.
"""

import json
import sys
import argparse
from pathlib import Path
from agents.guardrail_agent import GuardrailAgent

def load_agent_output(filepath: str) -> dict:
    """Load agent output from JSON file"""
    with open(filepath, 'r', encoding='utf-8') as f:
        return json.load(f)

def run_audit(agent_name: str, agent_phase: str, agent_output_file: str) -> dict:
    """
    Run GuardrailAgent audit with the specified parameters.
    
    Args:
        agent_name: Name of the agent being audited
        agent_phase: "Audit Phase 1" or "Audit Phase 2"
        agent_output_file: Path to the agent output JSON file
        
    Returns:
        Audit report in the specified JSON format
    """
    # Load agent output
    agent_output = load_agent_output(agent_output_file)
    
    # Initialize GuardrailAgent
    guardrail = GuardrailAgent()
    
    # Run audit
    result = guardrail.audit_agent_output(
        agent_name=agent_name,
        agent_phase=agent_phase,
        agent_output=agent_output
    )
    
    return result

def main():
    """Main entry point for production usage"""
    parser = argparse.ArgumentParser(
        description="Run GuardrailAgent audit on agent output",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Audit FileValidationAgent output
  python run_guardrail_production.py FileValidationAgent "Audit Phase 1" test_data/file_validation_output.json
  
  # Audit DataValidationAgent output
  python run_guardrail_production.py DataValidationAgent "Audit Phase 2" test_data/data_validation_output.json
        """
    )
    
    parser.add_argument('agent_name', 
                       help='Name of the agent being audited (e.g., FileValidationAgent)')
    parser.add_argument('agent_phase', 
                       help='Audit phase ("Audit Phase 1" or "Audit Phase 2")')
    parser.add_argument('agent_output_file', 
                       help='Path to the agent output JSON file')
    parser.add_argument('--output', '-o', 
                       help='Output file for audit report (default: stdout)')
    parser.add_argument('--pretty', '-p', action='store_true',
                       help='Pretty print JSON output')
    
    args = parser.parse_args()
    
    try:
        # Validate inputs
        if not Path(args.agent_output_file).exists():
            print(f"Error: Agent output file not found: {args.agent_output_file}", file=sys.stderr)
            sys.exit(1)
        
        if args.agent_phase not in ["Audit Phase 1", "Audit Phase 2"]:
            print(f"Error: Invalid agent_phase. Must be 'Audit Phase 1' or 'Audit Phase 2'", file=sys.stderr)
            sys.exit(1)
        
        # Run audit
        result = run_audit(args.agent_name, args.agent_phase, args.agent_output_file)
        
        # Format output
        if args.pretty:
            output = json.dumps(result, indent=2, ensure_ascii=False)
        else:
            output = json.dumps(result, ensure_ascii=False)
        
        # Write output
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(output)
            print(f"Audit report written to: {args.output}")
        else:
            print(output)
        
        # Exit with appropriate code based on status
        if result['status'] == 'CRITICAL':
            sys.exit(2)
        elif result['status'] == 'WARNING':
            sys.exit(1)
        else:
            sys.exit(0)
            
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        sys.exit(3)

if __name__ == "__main__":
    main()
